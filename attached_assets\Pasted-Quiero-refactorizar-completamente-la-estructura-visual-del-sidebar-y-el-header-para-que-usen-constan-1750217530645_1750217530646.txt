Quiero refactorizar completamente la estructura visual del sidebar y el header para que usen constantes centralizadas de UI, sin hardcodear medidas ni estilos en los archivos directamente. Todos los estilos deben venir de constantes globales, para que podamos cambiar fácilmente el aspecto de toda la app desde un solo lugar.

### 1. Crear constantes globales de UI

Crea un archivo en `client/src/lib/constants/ui.ts` con estas constantes:

```ts
export const SIDEBAR_WIDTH = 40
export const BUTTON_SIZE = 40
export const ICON_SIZE = 20
export const HEADER_HEIGHT = 40
2. Refactorizar Sidebar.tsx
El sidebar cerrado debe tener un ancho de SIDEBAR_WIDTH en píxeles.

Los botones del sidebar deben medir BUTTON_SIZE x BUTTON_SIZE.

Los íconos dentro de los botones deben tener ICON_SIZE x ICON_SIZE.

Todos los botones (incluyendo el logo arriba y el botón de settings abajo) deben estar centrados visualmente.

El sidebar debe seguir mostrando texto solo al hacer hover (como ya está implementado), pero toda la lógica visual debe basarse en estas constantes.

Creá un componente reutilizable SidebarButton en client/src/components/ui/SidebarButton.tsx que reciba un ícono (LucideIcon), un estado activo opcional y lo renderice usando BUTTON_SIZE e ICON_SIZE.

3. Refactorizar Header.tsx (o eliminar si ya no se usa)
Si el header todavía se usa, debe tener una altura de HEADER_HEIGHT y no más.

Todo debe estar alineado a la izquierda y respetar márgenes internos proporcionales al nuevo sistema visual.

4. Principios clave
NO hardcodees ningún w-10, h-10, text-xl, etc.

Usa siempre las constantes de ui.ts.

Todos los íconos deben respetar ICON_SIZE.

Todos los botones deben respetar BUTTON_SIZE.

Los estilos deben centralizarse para que luego podamos hacer temas (compacto, mobile, etc.).

Al finalizar, asegurate de que la estructura visual del sidebar esté completamente alineada, limpia y escalable. No cambies nada del comportamiento funcional (hover, navegación, etc.), solo la parte visual.