Quiero transformar el sidebar de la app para que sea **escalable y tenga dos niveles**:

---

### 🔁 Comportamiento general

- El sidebar principal debe ser **siempre compacto**: solo íconos, alineados verticalmente.
- Al hacer clic en un botón del sidebar principal, se debe abrir un **sidebar secundario**, que muestra los sub-items relacionados a ese grupo.
- El sidebar secundario se debe mostrar justo al lado, con botones de texto únicamente.

---

### 🧠 Estructura visual

- En el sidebar secundario, la parte superior (donde va el logo en el principal) debe mostrar el **nombre del grupo activo** (por ejemplo: "Finanzas", "Organización", etc.).
- El diseño debe imitar el layout de apps como Notion, Linear o los ejemplos que te mostré.
- El **alto y separación entre ítems del sidebar secundario** deben ser idénticos a los del principal.
- Los botones secundarios deben tener solo texto y ocupar todo el ancho del panel.

---

### ⚙️ Estructura funcional

El sidebar principal debe tener estos grupos, con sus íconos actuales:

1. **Dashboard** → sin submenú (lleva directamente a la página Dashboard).
2. **Organización** → muestra:
   - Gestión de Organizaciones
   - Contactos
3. **Proyectos** → muestra:
   - Gestión de Proyectos
4. **Obra** → muestra:
   - Bitácora de Obra
5. **Finanzas** → muestra:
   - Movimientos
6. **Configuración** → muestra:
   - Admin de Organizaciones (AdminOrganizations.tsx)
   - Ver perfil

---

### 📦 Organización del código

- Refactorizá `Sidebar.tsx` para dividir en:
  - Sidebar principal (íconos)
  - Sidebar secundario (submenú dinámico según ítem activo)
- Mantené el control de qué grupo está activo mediante Zustand o un `useState` persistente si es más práctico.
- El componente que renderiza los subitems puede llamarse `SidebarSubmenu`.

---

### 🛑 Restricciones

- No rompas nada del layout principal ni de las rutas.
- No toques `App.tsx` ni `Router.tsx`.
- Asegurate de que los sub-items mantengan su navegación actual (`Link` o `useNavigate()`).
- No modifiques los permisos ni la lógica de autenticación.

---

¿Podés implementar todo esto con el diseño y comportamiento solicitado?