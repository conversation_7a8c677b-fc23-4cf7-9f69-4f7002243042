Quiero que crees una página nueva llamada `Contacts.tsx` siguiendo TODAS estas especificaciones y lógicas ya implementadas en otras secciones de la app:

---

## 🧱 Estructura general:

1. <PERSON><PERSON> `CustomPageLayout` con:
   - `icon` adecuado (ej: AddressBook o User)
   - `title: "Contactos"`
   - `actions`: botón “Nuevo contacto”
   - `customFilters`: dropdown con filtros (ver más abajo)
   - `onClearFilters`: debe limpiar todos los filtros aplicados

---

## 🗂️ Base de datos involucrada:

### Tabla `contacts`
- id (uuid)
- organization_id (uuid)
- first_name (text)
- last_name (text)
- email (text)
- phone (text)
- company_name (text)
- location (text)
- notes (text)
- created_at (timestamp)

### Tabla `contact_types`
- id (uuid)
- name (text)
- created_at (timestamp)

⚠️ Cada contacto tiene un `contact_type_id` que se debe agregar como FK en la consulta (aunque no aparezca aún en la tabla de `contacts`, simular que existe).

---

## 🎛️ Filtros dinámicos:

Dentro de `customFilters`, mostrá los siguientes campos:

1. Select para filtrar por `Tipo de contacto` (usando los datos de `contact_types`)
2. Select para ordenar por:
   - Nombre A-Z
   - Nombre Z-A
   - Fecha reciente
   - Fecha antigua

Debe haber un botón “Limpiar filtros” (fuera del dropdown) que resetee todos los filtros.

---

## 🔁 Funcionalidad:

- Mostrar listado tipo tabla o tarjetas.
- Mostrar:
  - Nombre completo
  - Email
  - Teléfono
  - Tipo de contacto
  - Empresa (si existe)
  - Ciudad o ubicación (si existe)
- Cada entrada debe tener botón de editar y eliminar (usando dropdown).

---

## 📦 Modal de creación/edición:

Creamos un modal `CreateContactModal` con los siguientes campos:
- Nombre
- Apellido
- Email
- Teléfono
- Tipo de contacto (Select desde `contact_types`)
- Nombre de empresa
- Ubicación
- Notas

El modal debe soportar modo edición (si se pasa `editingContact`) o creación (nuevo).

---

## ✅ Detalles a tener en cuenta:

- Usar el `organization_id` del usuario actual (`userData.preferences.last_organization_id`) para filtrar y guardar.
- Usar React Query para consultar datos.
- Al eliminar un contacto, pedir confirmación (`AlertDialog`).
- Al guardar o eliminar, usar `toast()` y `queryClient.invalidateQueries()`.

---

## 📄 Archivos esperados:

1. `Contacts.tsx` – Página completa
2. `CreateContactModal.tsx` – Modal con validación (Zod)
3. Hook opcional: `useContactTypes()` si necesitás consultar `contact_types`

---

Este módulo debe seguir el mismo diseño, organización de código, componentes y experiencia de usuario que `SiteLogs.tsx` y sus modales. Usá los mismos nombres de componentes, estilos y patrones de estado (`expandedCard`, `useState`, etc.) que ya usamos en las otras secciones.