### OBJETIVO

Quiero eliminar el componente `CustomPageHeader` y unificar toda su lógica y diseño dentro del `Header.tsx`, que ya tiene breadcrumb. Además, quiero que los botones que aparecen en el header tengan proporciones adecuadas y no desborden su altura.

---

### 1. MIGRAR CustomPageHeader a Header

- Eliminá completamente el uso de `CustomPageHeader.tsx` en la app.
- `Header.tsx` debe incorporar todo lo que antes iba en ese componente:
  - Breadcrumb (ya está)
  - Buscador (si corresponde)
  - Botón de filtros (dropdown)
  - Botón de limpiar filtros
  - Acciones como “Nuevo Proyecto”, “Exportar PDF”, etc.

---

### 2. NUEVA INTERFAZ DE PROPS PARA Header.tsx

Agregá estas props al Header:

```ts
interface HeaderProps {
  title?: string // opcional
  showSearch?: boolean
  searchValue?: string
  onSearchChange?: (value: string) => void
  showFilters?: boolean
  filters?: { label: string; onClick: () => void }[]
  customFilters?: React.ReactNode
  onClearFilters?: () => void
  actions?: React.ReactNode
}
3. ESTRUCTURA VISUAL DEL HEADER
Usá un div contenedor con flex items-center justify-between h-10 px-4 gap-2.

A la izquierda siempre va el breadcrumb.

A la derecha (ml-auto) colocá:

Buscador

Filtros (dropdown)

Botón de limpiar filtros

Acciones (como un botón primario o varios)

4. BOTONES Y ALTURA UNIFICADA
Los botones que se coloquen dentro del header deben tener la altura correcta:

tsx
Copiar
Editar
<Button className="h-8 px-3 text-sm">
  Acción
</Button>
Esto asegura que encajen dentro del h-10 (40px) del header sin sobresalir ni desalinearse verticalmente.

5. POSICIONAMIENTO Y STICKY
El Header.tsx debe seguir siendo sticky top-0 z-50 bg-background

No debe haber ningún margin-top o padding-top entre el header y el contenido

El contenido (renderizado por CustomPageLayout o CustomPageBody) debe comenzar justo debajo del header

6. ACTUALIZAR TODAS LAS PÁGINAS
Reemplazá CustomPageHeader por Header en todas las vistas que lo usaban, pasando los props necesarios (title, actions, filters, etc.).

