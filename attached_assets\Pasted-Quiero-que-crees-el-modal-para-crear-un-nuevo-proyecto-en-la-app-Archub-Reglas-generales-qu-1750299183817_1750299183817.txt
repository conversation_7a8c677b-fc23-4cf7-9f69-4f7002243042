Quiero que crees el modal para crear un nuevo proyecto en la app Archub.

🧩 **Reglas generales que ya deberías conocer**:
- Usá los componentes definidos en `prompts/ai-page-template.md` y `prompts/ai-modal-template.md`
- El botón ya existe en la página de Proyectos. Solo tenés que vincular el modal al click de ese botón.
- Todos los componentes del modal ya están creados y se encuentran en `src/components/ui-custom/`:
  - `CustomModalLayout`
  - `CustomModalHeader`
  - `CustomModalBody`
  - `CustomModalFooter`

🛠 **Funcionalidad esperada**:
- Este modal debe permitir **crear un nuevo proyecto real**, conectado directamente con Supabase.
- Al guardar, debe crear una fila en la tabla `projects`, y otra en la tabla `project_data`, que ya están creadas en Supabase.
- No uses datos de ejemplo ni mocks. Traé y envía **datos reales de Supabase**.
- Si es necesario usar React Query para mutations, hacelo bien con `useMutation` y `useQueryClient().invalidateQueries()` si aplica.

📐 **Estructura esperada**:
- `CustomModalLayout`: envoltorio principal
- `CustomModalHeader`: título y descripción del modal, con botón de cerrar
- `CustomModalBody`: formulario con campos necesarios
- `CustomModalFooter`: botones de "Cancelar" y "Guardar", como ya está diseñado

📦 **Campos mínimos del formulario**:
- `name`: nombre del proyecto
- `description`: descripción corta
- `start_date`: fecha de inicio (tipo `date`)
- `status`: select con valores posibles como "En curso", "Finalizado", etc. (si ya están en Supabase)
- Los valores deben ir a la tabla `projects`. Si `project_data` requiere alguna info base, también insertala.

🎯 **Extras**:
- Validá que no haya campos vacíos antes de guardar
- Al crear el proyecto correctamente, cerrá el modal y refrescá la lista de proyectos
- Que el nuevo proyecto quede seleccionado como el activo si es posible (actualizar `user_preferences.last_project_id` si corresponde)

🚫 **NO HAGAS ESTO**:
- No pongas ejemplos ficticios
- No uses datos hardcodeados
- No crees rutas nuevas ni layouts adicionales
- No inventes componentes nuevos

🎉 ¡Gracias! Recordá seguir las guías de estilo de Tailwind y los componentes personalizados que ya definimos.