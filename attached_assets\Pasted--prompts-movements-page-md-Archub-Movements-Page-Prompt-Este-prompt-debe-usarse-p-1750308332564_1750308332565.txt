---
📁 prompts/movements-page.md
---

# 🔄 Archub – Movements Page Prompt

Este prompt debe usarse para crear o mantener la página de **Movimientos** (`movements.tsx`) en el sistema Archub.
Debe seguir estrictamente los lineamientos visuales y técnicos definidos en `prompts/ai-page-template.md`.

---

## 📅 Propósito
La página `movements.tsx` permite visualizar, registrar y gestionar movimientos financieros dentro de un proyecto.

---

## 📝 Comportamiento esperado

### 1. Estructura Visual
- Utilizar `CustomPageLayout`, `CustomPageHeader`, `CustomSearchButton`, `CustomTable`.
- Representar cada movimiento como una **fila** en una tabla completa (no usar cards).
- Mostrar filtros y búsqueda arriba.
- Botón de "Nuevo Movimiento" que abre un modal.

### 2. Campos visibles por movimiento (columnas):
- <PERSON><PERSON> del movimiento (`created_at`)
- <PERSON><PERSON><PERSON><PERSON> (`description`)
- <PERSON><PERSON> (`amount`)
- T<PERSON><PERSON> (`type_id` → movement_concepts.name)
- Categoría (`category_id` → movement_concepts.name)
- Moneda (`currency_id` → currencies.code)
- Billetera (`wallet_id` → wallets.name)
- Creado por (`created_by` → users.full_name + avatar)
- Acciones (Editar / Eliminar)

### 3. Modal de nuevo movimiento
- Compatible con desktop (anclado a la derecha) y mobile (100% alto).
- Componentes: `CustomModalLayout`, `CustomModalHeader`, `CustomModalBody`, `CustomModalFooter`
- Inputs necesarios:
  - Fecha del movimiento (editable)
  - Descripción (textarea)
  - Monto
  - Moneda (select desde `currencies`)
  - Tipo (select desde `movement_concepts` donde `parent_id IS NULL`)
  - Categoría (select desde `movement_concepts` donde `parent_id = selected_type_id`)
  - Billetera (select desde `wallets` de la organización)
- Automáticamente debe guardar:
  - `created_by` como `organization_members.id`
  - `organization_id`, `project_id` y `created_at` (editable)

### 4. Lógica de relaciones
- Usar la tabla `movement_concepts` como jerárquica:
  - Si `parent_id IS NULL` es tipo
  - Si `parent_id = uuid` es categoría
- Evitar valores hardcodeados: todo se obtiene vía Supabase.
- Filtrar movimientos por organización activa (`organization_id`) y proyecto activo (`project_id`).

---

## ⛔ Evitar
- No inventar campos.
- No usar valores de ejemplo.
- No usar textos hardcodeados.
- No traer datos directos con `supabase.from()`; usar `rpc` si aplica.

---

## ✅ Resultado Esperado
Una página profesional, clara, 100% funcional, sin errores ni supuestos. Compatible con escritorio y mobile. Con el modal integrado para crear nuevos movimientos de manera intuitiva y ordenada.
