Quiero que refactorices por completo el sidebar de mi aplicación para que funcione y se vea igual que el de Supabase. Estas son las especificaciones precisas:

---

### 🎯 FUNCIONALIDAD GENERAL

- El sidebar debe estar alineado a la izquierda, con **ancho cerrado de 40px** y **ancho expandido de 240px**.
- Al hacer **hover sobre el sidebar completo**, debe expandirse suavemente sin mover los íconos.
- Los íconos deben tener **tamaño 20x20px**, y los botones deben ser de **32x32px** centrados.
- Los **íconos no deben moverse ni cambiar de posición** al aparecer el texto.
- El texto debe aparecer al costado con una animación suave, SIN empujar el icono.

---

### 🔲 BOTONES SUPERIORES

Estos botones deben ser renderizados desde una lista de navegación dinámica (ejemplo: `sidebarItems`), cada uno con:

- `icon` (icono de lucide-react o shadcn/ui),
- `label` (texto a mostrar),
- `href` (ruta a navegar),
- `section` (para agrupar por sección si hay muchas).

---

### 🔧 BOTONES INFERIORES FIJOS

Abajo del todo, SIEMPRE deben estar estos tres botones:

1. **Admin** (icono de engranaje).
2. **Theme** (icono de luna o sol, togglear dark/light).
3. **Perfil** (mostrar avatar redondo + dropdown al hacer clic).

Estos botones:

- No deben desaparecer nunca.
- Deben conservar el mismo espaciado y padding que los de arriba.
- Deben estar alineados a la izquierda si el sidebar está abierto.

---

### 🎨 ESTILO VISUAL

- Fondo: `bg-background`.
- Botón activo: `bg-muted text-foreground font-semibold`.
- Hover: `hover:bg-muted transition-colors`.
- El botón expandido debe mostrar texto a la derecha del icono, con fuente `text-sm`.

---

### 🧠 ESTADO Y ANIMACIONES

- El estado de expansión (hover) debe manejarse con `group/sidebar`, usando `group-hover:block`, `group-hover:w-[240px]`, etc.
- Usa Tailwind `transition-all`, `duration-300`, `ease-in-out`.

---

### 🧩 COMPONENTES

Crea o ajusta estos:

- `Sidebar.tsx`: Estructura principal y lógica de expansión.
- `SidebarButton.tsx`: Componente reutilizable para los botones del sidebar.
- `SidebarSection.tsx`: Agrupador de botones si hay secciones.
- `sidebarItems.ts`: Lista con ítems principales.

---

### 🧠 ESTADO GLOBAL

Utiliza un `sidebarStore.ts` con Zustand para guardar si el sidebar está:

- `docked` (siempre abierto),
- `hovered` (si el mouse está encima).

Usa el campo `sidebar_docked` de la tabla `user_preferences` para sincronizar.

---

### 🧼 IMPORTANTE

1. No modifiques el layout general ni el header.
2. No borres los botones de abajo.
3. Usa íconos de `lucide-react`.
4. No pongas padding innecesario en los botones.
5. Asegúrate de que la experiencia visual sea idéntica a la de Supabase (puedes ver la referencia en las imágenes del usuario).