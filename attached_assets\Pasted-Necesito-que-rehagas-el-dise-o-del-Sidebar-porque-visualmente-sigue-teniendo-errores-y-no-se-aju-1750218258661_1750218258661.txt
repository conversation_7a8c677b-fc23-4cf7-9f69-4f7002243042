Necesito que rehagas el diseño del Sidebar porque visualmente **sigue teniendo errores** y no se ajusta al sistema visual que estamos construyendo. Los problemas son estos:

---

## ❌ Problemas actuales:

1. Los botones tienen **padding o márgenes externos innecesarios**.  
   👉 Cuando el sidebar está cerrado, debería verse **solo el botón** centrado (sin espacio extra, sin texto oculto flotante, sin offset).

2. El área del botón **no está centrada ni vertical ni horizontalmente**.  
   👉 Los íconos deben estar **exactamente al medio del ancho del sidebar** y con spacing vertical regular (ej. 8px entre ítems).

3. El botón de “Settings” abajo y el logo arriba deben tener **exactamente el mismo diseño que los demás ítems**.  
   👉 Mismo `SidebarButton`, mismo tamaño, sin excepciones.

4. El texto actualmente aparece “al lado”, pero **no forma parte del botón**.  
   👉 Todo el ítem debe ser una única unidad clickable (ícono + texto dentro del mismo `button` o `a`).

5. Cuando el sidebar está cerrado, los ítems deben ocupar exactamente `BUTTON_SIZE x BUTTON_SIZE` (ej: 40x40 px), **sin margen, sin relleno, sin texto, sin espacio vacío**.

---

## ✅ Lo que quiero:

1. Todos los botones deben tener este estilo exacto (cuando el sidebar está cerrado):

```ts
// SidebarButton.tsx
<button
  className="flex items-center justify-center"
  style={{ width: BUTTON_SIZE, height: BUTTON_SIZE }}
>
  <Icon className="text-foreground" style={{ width: ICON_SIZE, height: ICON_SIZE }} />
</button>
Cuando el sidebar está abierto (hover), el botón se transforma en:

ts
Copiar
Editar
<button
  className="flex items-center px-3 gap-2 rounded-lg transition-colors"
  style={{ height: BUTTON_SIZE }}
>
  <Icon style={{ width: ICON_SIZE, height: ICON_SIZE }} />
  <span className="text-sm font-medium">Nombre</span>
</button>
Todo debe ser un solo botón (o Link), no separado.

El logo arriba, los ítems del nav y el botón Settings usan el mismo SidebarButton con las mismas reglas. No debe haber ningún diseño especial ni diferente.

Los ítems deben tener espaciado vertical de 8px como máximo. No uses padding-top ni margin extra.

Los textos deben aparecer solo si el sidebar está expandido, y deben estar alineados al ícono dentro del mismo botón. No queremos spans flotantes con group-hover ni nada separado.

🔁 La interacción con hover debe modificar internamente si el sidebar está expandido o no, y renderizar el botón completo o compacto según eso.
NO uses group-hover para mostrar u ocultar el texto de forma externa.

📌 Recordá usar las constantes de diseño:

ts
Copiar
Editar
SIDEBAR_WIDTH = 40
SIDEBAR_EXPANDED_WIDTH = 200
BUTTON_SIZE = 40
ICON_SIZE = 20
PADDING_MD = 8
yaml
Copiar
Editar
