Quiero que realices una mejora completa del sistema de usuario y configuración visual del proyecto. Debe incluir:

---

✅ 1. Agregar botón de perfil/avatar en el sidebar

- Justo debajo del botón de “Settings” (`IconSettings`)
- El botón debe ser circular (`rounded-full`, `h-10 w-10`) y mostrar el `avatar_url` del usuario actual
- Si no tiene avatar, mostrar iniciales (como en Supabase)
- Al hacer clic, redirige a la nueva ruta `/perfil`

---

✅ 2. Crear página `/perfil` del usuario

- Ruta: `/perfil`
- Componente: `ProfilePage.tsx`
- Debe usar `CustomPageLayout` y `CustomPageHeader` (título: "Mi Perfil", ícono: `User`)
- Mostrará:

```ts
// users table
- full_name
- email (solo lectura)
- role (badge de admin o user)
- avatar_url (editable con subida o con URL externa)

// user_preferences
- theme (dark/light, con toggle)
- sidebar_docked (sí/no, con switch)

// user_data
- country (select con lista de países)
- age (número)
- discovered_by (campo tipo "¿Cómo nos conociste?")
Todos los campos deben ser editables excepto el email

Usar react-hook-form o lógica que ya tengan en el proyecto

Al guardar, actualiza las 3 tablas: users, user_preferences, user_data

Mostrar feedback visual (toast o success)

✅ 3. Soporte para cambio de avatar

Permitir:

Subir imagen desde disco

Pegar URL pública

Mostrar preview del avatar

Validar formato (jpg/png) y tamaño máximo

✅ 4. Traducir toda la UI del sistema al español

Todos los botones, placeholders, mensajes, headers, tooltips, campos y menús deben estar en español

Ejemplos:

"New Project" → "Nuevo Proyecto"

"Filters" → "Filtros"

"Search..." → "Buscar..."

"Sign Out" → "Cerrar sesión"

"Dark Mode" → "Modo oscuro"

Asegurarse de traducir DropdownMenuItem, Button, Badge, PageHeader, EmptyState, etc.

🎯 Consideraciones:

Usar Tailwind como hasta ahora

No hardcodear traducciones en texto plano si ya existe algún helper (pero no hace falta i18n por ahora)

Todo debe integrarse visual y funcionalmente con el sistema actual

yaml
Copiar
Editar
