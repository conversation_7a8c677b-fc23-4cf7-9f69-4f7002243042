Quiero que crees los siguientes tres archivos y los guardes en sus respectivas carpetas:

1. `src/components/ui-custom/CustomRestricted.tsx`
2. `src/hooks/usePlanFeatures.ts`
3. `src/utils/restrictions.ts`

---

### 1. CustomRestricted.tsx

Este componente se usa para **bloquear contenido de la interfaz** cuando:

- El usuario superó un límite de su plan (ej: max_projects).
- El plan no incluye una funcionalidad (ej: export_pdf_custom).
- La funcionalidad está marcada como "coming soon", sin importar el plan.

🔧 Props:
- `feature?: string` – nombre de la feature a evaluar.
- `current?: number` – valor actual (para evaluar si supera el límite).
- `reason?: "coming_soon" | string` – alternativa para bloquear algo por otro motivo.
- `children: React.ReactNode` – lo que se renderiza normalmente (botón, opción, card, etc.).

🧠 Comportamiento:
- Si se detecta que está restringido (por feature o reason), debe:
  - Aplicar `relative`, `blur-[2px]`, `pointer-events-none`, `opacity-60`.
  - Mostrar un ícono de candado (`🔒`) centrado encima (`absolute`).
  - Al hacer hover, mostrar un `Popover` con un mensaje explicativo y opcionalmente un botón con link para "Actualizar plan".
- Si no está restringido, renderiza directamente el `children`.

💅 Usá Tailwind para todos los estilos.

---

### 2. usePlanFeatures.ts

Hook que devuelve funciones útiles para consultar el plan de la organización actual.

Debe devolver:

```ts
{
  features: Record<string, any>;
  can: (feature: string) => boolean;
  limit: (feature: string) => number;
}
Extrae los datos del plan desde el contexto o store donde esté guardada la organización activa, asumiendo que tiene organization.plan?.features disponible como JSON.

3. restrictions.ts
Archivo de utilidades que mapea los motivos de restricción (feature o reason) a un mensaje y un botón de acción opcional.

Ejemplo del formato:

ts
Copiar
Editar
export const restrictionMessages = {
  export_pdf_custom: {
    message: "Esta funcionalidad está disponible solo en el plan PRO.",
    actionLabel: "Actualizar plan",
    actionUrl: "/billing",
  },
  max_projects: {
    message: "Ya alcanzaste el máximo de proyectos para tu plan actual.",
    actionLabel: "Ver planes",
    actionUrl: "/billing",
  },
  coming_soon: {
    message: "Esta función estará disponible próximamente.",
    actionLabel: "",
    actionUrl: "",
  },
  // ...otros
};
El componente CustomRestricted debe leer desde acá según el feature o reason.

🎯 El objetivo es que yo pueda usar este componente así:

tsx
Copiar
Editar
<CustomRestricted feature="export_pdf_custom">
  <BotonDescargar />
</CustomRestricted>

<CustomRestricted feature="max_projects" current={projects.length}>
  <Button>Nuevo Proyecto</Button>
</CustomRestricted>

<CustomRestricted reason="coming_soon">
  <Button>Exportar a IA</Button>
</CustomRestricted>
Asegurate de que:

El componente renderice correctamente sobre cualquier tipo de children.

La lógica de restricción esté clara y centralizada.

El popover se vea bien y tenga sentido semántico y visual.

Cada archivo debe guardarse en su carpeta correspondiente. Usá buen diseño de código, buena legibilidad y mantené consistencia con el resto del proyecto.