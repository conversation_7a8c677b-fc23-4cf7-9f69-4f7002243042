🧠 Prompt para Replit: Refactorización de la página de proyectos

Estamos trabajando en una app llamada Archub. Ya tenemos una estructura SPA bien definida con componentes reutilizables. También creamos un archivo llamado ai-page-template.md donde se especifica la estructura visual que deben seguir las páginas, y otro llamado ai-modal-template.md para los modales. Ambos están en la carpeta /prompts y deben ser usados como contexto previo obligatorio antes de escribir o modificar código.

Ahora quiero que tomes la página de proyectos (Projects.tsx) y la modifiques con los siguientes objetivos:

✅ 1. Rediseñar la vista de proyectos
En lugar de usar un layout tipo "card" por proyecto, quiero que toda la lista de proyectos aparezca como una tabla de ancho completo, al estilo de la interfaz que te adjunto como referencia visual (archivo llamado 6c9e77c7.png).
Cada fila representa un proyecto. Las columnas deben ser:

<PERSON><PERSON> de creación (created_at)

Miembro creador (created_by) — Mostrar el avatar y nombre

Nombre del proyecto (name) — clickable (selecciona el proyecto)

Estado del proyecto (status) — usar Badge

Acciones — Botones para editar (abre modal) y eliminar (con confirmación)

✅ 2. Funcionalidad de selección de proyecto
Al hacer clic en el nombre del proyecto, se debe ejecutar la misma lógica actual de selección de proyecto (con useMutation → user_preferences.last_project_id).
Debe marcarse visualmente el proyecto seleccionado (por ejemplo con un ícono 👑 como ahora).

✅ 3. Editar proyecto desde la tabla
El botón "Editar" debe abrir el modal ya creado (NewProjectModal) pero en modo edición.
Si es necesario, duplicá la lógica actual del modal para permitir modo edición con valores iniciales y update.

✅ 4. Eliminar proyecto
El botón "Eliminar" debe mostrar un modal de confirmación (confirm() o AlertDialog) y luego eliminar el proyecto de la base de datos. Usar Supabase y manejar errores con toast.

✅ 5. Consideraciones técnicas
El campo created_by es un UUID que apunta a organization_members.id, pero la información del usuario debe venir de organization_member_details o directamente de user.full_name y user.avatar_url si está disponible en el contexto de usuario.

Ya están disponibles los datos de userData con memberships, user, organization, etc.

Usar componentes de UI ya definidos en @/components/ui y @/components/ui-custom.

Las clases de estilo deben seguir el sistema index.css (no usar estilos inline).

Usar useQuery y useMutation de React Query para cualquier cambio de estado.

Actualizar queryClient cuando se cree, edite o borre un proyecto.

Refactorá sin romper la estructura actual de Projects.tsx, manteniendo CustomPageLayout, searchValue, filtros, etc.

🎯 El objetivo es que esta nueva vista sea más limpia, profesional, editable y escale mejor a medida que hay más proyectos.
No agregues datos de ejemplo. Usá lo que venga de Supabase.

¿Listo? ¡Adelante!