Quiero que reestructures cómo renderizamos las páginas en la app. Vamos a crear una arquitectura reusable con layout + header + contenido.

---

🔧 1. Creá un nuevo archivo en `client/src/components/ui-custom/CustomPageLayout.tsx`.

Este componente debe:

- Renderizar un contenedor general (`div`) que centre el contenido (`justify-center`)
- <PERSON><PERSON>, tener un `div` con `max-w-[1440px] px-4` por defecto
- Si recibe `wide={true}`, debe ocupar todo el ancho (`max-w-none`)
- Este `div` interno contendrá el `CustomPageHeader` y debajo el `children` (contenido de la página)

Agregale un `border` de color rojo para debug visual.

---

🔧 2. Modificá el componente `CustomPageHeader.tsx` (que ya existe):

- Debe dejar de ocupar `w-full`
- Su contenido debe adaptarse al ancho del contenedor padre
- La altura total debe ser 76px, dividida en dos filas de exactamente 38px cada una
- La primera fila: ícono + título a la izquierda, botones (`actions`) a la derecha
- La segunda fila: campo de búsqueda (100% de ancho), botón de filtros con dropdown dinámico, y botón de "Limpiar filtros"

Agregale `border` de color verde a este header para debug.

---

📄 3. Aplicalo ya en las siguientes páginas:

- `pages/Dashboard.tsx`
- `pages/Organizations.tsx`

En ambas páginas:

- Reemplazá el uso de `PageHeader` por `CustomPageLayout`
- Usá los props correctamente (`title`, `icon`, `actions`, `showSearch`, etc.)
- Como contenido `children`, dejá todo lo que estaba debajo del header original

Agregale un `border` de color azul al bloque del `children` para ver dónde empieza.

---

📁 4. Ubicación de archivos:

- `CustomPageLayout.tsx` debe ir en `client/src/components/ui-custom/`
- El componente `CustomPageHeader.tsx` ya está en esa carpeta

---

🎯 El objetivo es que a partir de ahora todas las páginas usen este layout reusable. Queremos que header y contenido estén perfectamente alineados y centrados dentro de un contenedor común (1440px máximo por defecto).
