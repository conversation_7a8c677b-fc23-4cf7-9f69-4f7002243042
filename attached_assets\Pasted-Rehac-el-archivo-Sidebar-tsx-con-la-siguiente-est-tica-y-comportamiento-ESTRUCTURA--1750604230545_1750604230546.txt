Rehacé el archivo `Sidebar.tsx` con la siguiente estética y comportamiento:

---

🔹 ESTRUCTURA Y ESTILO

- El sidebar debe tener `width: 40px` por defecto (`w-[40px]`)
- Cada botón debe ser un `button` de `32x32px` (`h-8 w-8`), con `rounded-md`
- El ícono dentro del botón debe medir `20x20px` (`h-5 w-5`)
- <PERSON> hover, el botón debe mostrar el texto del label a la derecha del icono, con animación suave, **sin empujar el ícono**
- El texto debe tener `position: absolute`, `left-full`, `ml-2`, con `opacity-0` y aparecer con `group-hover:opacity-100`
- Agregar `transition-all duration-300` a todos los elementos con animación
- Cuando el botón está activo o seleccionado, debe mostrar un fondo sutil (ej: `bg-muted`) y cambiar el color del ícono (ej: `text-primary`)
- El layout debe tener padding `py-2` y espaciado entre los botones
- El botón activo debe distinguirse claramente con un color o fondo
- Cada botón debe tener `title` para accesibilidad

---

🔹 COMPORTAMIENTO

- Al hacer hover en el sidebar (group parent), **no debe empujar ni mover los íconos**
- El texto aparece flotando a la derecha, como en Supabase
- El estado de cada botón puede evaluarse con la ruta activa actual (`pathname`)

---

🔹 EJEMPLO DE UN BOTÓN

```tsx
<button
  className={cn(
    'group relative flex items-center justify-center',
    'h-8 w-8 rounded-md transition-all duration-300',
    pathname === path ? 'bg-muted text-primary' : 'hover:bg-muted hover:scale-105'
  )}
  title={label}
  onClick={() => router.push(path)}
>
  <Icon className="h-5 w-5" />

  <span
    className={cn(
      'absolute left-full ml-2 whitespace-nowrap',
      'opacity-0 group-hover:opacity-100',
      'translate-x-1 group-hover:translate-x-0',
      'transition-all duration-300 text-sm text-muted-foreground'
    )}
  >
    {label}
  </span>
</button>
🔹 CONSIDERACIONES

Usar useNavigationStore() si corresponde para saber qué sección está activa

Aplicar esta lógica a todos los botones del sidebar

El sidebar debe funcionar bien tanto con modo claro como oscuro

Reemplazá por completo el contenido de Sidebar.tsx con esta lógica.