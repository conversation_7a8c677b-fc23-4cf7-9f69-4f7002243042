Necesito que unifiques el diseño visual de los siguientes componentes:

- Input (`input.tsx`)
- Select (`select.tsx`)
- Dropdown Menu (`dropdown-menu.tsx`)

---

🎯 Objetivo:
Que todos usen un sistema visual coherente, controlado por variables CSS (tokens) y clases de Tailwind consistentes, sin estilos hardcodeados.

---

✅ Cambios a realizar:

1. **Tokens de diseño global:**
   Usar siempre estas variables CSS:
   - `--input-bg`
   - `--input-text`
   - `--input-border`
   - `--input-placeholder`
   - `--input-radius`
   - `--input-ring`
   - `--accent-bg`
   - `--popover-bg`
   - `--popover-fg`
   - `--radius-sm`, `--radius-md`, `--radius-lg`

2. **Input (`input.tsx`):**
   Ya está bien implementado. Usalo como base.
   - Clases recomendadas:  
     `w-full h-10 px-3 py-2 text-sm border bg-[var(--input-bg)] text-[var(--input-text)] placeholder-[var(--input-placeholder)] rounded-[var(--input-radius)] border-[var(--input-border)] focus:ring-[var(--input-ring)] transition-colors`

3. **Select (`select.tsx`):**
   - El `SelectTrigger` debe copiar las mismas clases del Input.
   - Usar:
     - `bg-[var(--input-bg)]`
     - `text-[var(--input-text)]`
     - `placeholder-[var(--input-placeholder)]`
     - `rounded-[var(--input-radius)]`
     - `border-[var(--input-border)]`
     - `focus:ring-[var(--input-ring)]`
   - Eliminar clases hardcodeadas como `bg-background` o `text-sm`.

4. **Dropdown Menu (`dropdown-menu.tsx`):**
   - Usar:  
     `bg-[var(--popover-bg)] text-[var(--popover-fg)] border-[var(--input-border)] rounded-[var(--input-radius)]`
   - En los ítems (`DropdownMenuItem`), usar `hover:bg-[var(--accent-bg)] transition-colors`

5. **Consistencia tipográfica:**
   - Todos deben usar el mismo tamaño: `text-sm`
   - Todos deben tener `transition-colors` y `focus:outline-none` cuando corresponda

6. **Modo oscuro:**
   - Usar `.dark` para definir variables alternativas, como ya se hace en `index.css`

---

🧪 Bonus (opcional): 
Agregar una clase global tipo `.form-element` que tenga todas estas clases para evitar repetirlas y permitir extender.

---

📦 Resultado esperado:
Todos los campos de formulario deben verse homogéneos (inputs, selects, dropdowns, etc.), con bordes iguales, radios iguales, tamaños de texto iguales, y fondo coherente con el tema activo.
