@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Layout */
  --layout-bg: hsl(0, 0%, 99%);
  --layout-text: hsl(0, 0%, 45%);
  --layout-border: hsl(0, 0%, 45%);

  /* Menus/Sidebar */
  --menues-bg: hsl(0, 0%, 97%);
  --menues-fg: hsl(0, 0%, 45%);
  --menues-secondary-fg: hsl(0, 0%, 50%);
  --menues-border: hsl(0, 0%, 85%);
  --menues-hover-bg: hsl(0, 0%, 45%);
  --menues-hover-fg: hsl(0, 0%, 97%);
  --menues-active-bg: hsl(0, 0%, 45%);
  --menues-active-fg: hsl(0, 0%, 97%);

  /* Cards */
  --card-bg: hsl(0, 0%, 97%);
  --card-text: hsl(0, 0%, 35%);
  --card-border: hsl(0, 0%, 85%);
  --card-hover-bg: hsl(240, 3%, 98%);

  /* Buttons - Primary */
  --button-primary-bg: hsl(90, 50%, 50%);
  --button-primary-text: hsl(0, 0%, 100%);
  --button-primary-border: transparent;
  --button-primary-hover-bg: hsl(90, 50%, 50%);
  --button-primary-hover-text: hsl(0, 0%, 100%);
  --button-primary-hover-border: transparent;
  --button-primary-disabled-bg: hsl(240, 5%, 85%);
  --button-primary-disabled-text: hsl(240, 5%, 65%);

  /* Buttons - Secondary */
  --button-secondary-bg: hsl(0, 0%, 100%);
  --button-secondary-text: hsl(240, 10%, 4%);
  --button-secondary-border: hsl(240, 6%, 90%);
  --button-secondary-hover-bg: hsl(240, 5%, 96%);
  --button-secondary-hover-text: hsl(240, 10%, 4%);
  --button-secondary-hover-border: hsl(240, 8%, 80%);

  /* Buttons - Ghost */
  --button-ghost-bg: transparent;
  --button-ghost-text: hsl(0, 0%, 35%);
  --button-ghost-border: transparent;
  --button-ghost-hover-bg: hsl(240, 5%, 96%);
  --button-ghost-hover-text: hsl(240, 10%, 4%);
  --button-ghost-hover-border: transparent;

  /* Inputs */
  --input-bg: hsl(0, 0%, 100%);
  --input-text: hsl(240, 10%, 4%);
  --input-border: hsl(240, 6%, 90%);
  --input-placeholder: hsl(240, 5%, 65%);
  --input-focus-ring: hsl(90, 50%, 50%);
  --input-focus-border: hsl(90, 50%, 50%);

  /* Popover/Dropdown */
  --popover-bg: hsl(0, 0%, 100%);
  --popover-fg: hsl(240, 10%, 4%);
  --accent-bg: hsl(240, 5%, 96%);

  /* Accent / State */
  --accent: hsl(90, 50%, 50%);
  --accent-text: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-text: hsl(0, 0%, 100%);
  --success: hsl(120, 40%, 50%);
  --success-text: hsl(0, 0%, 100%);
  --warning: hsl(45, 90%, 50%);
  --warning-text: hsl(240, 10%, 4%);

  /* Charts */
  --chart-1: hsl(110, 40%, 50%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(0, 87%, 67%);

  /* Text */
  --text-default: hsl(240, 10%, 4%);
  --text-muted: hsl(240, 5%, 45%);
  --text-subtle: hsl(240, 5%, 65%);
  --text-inverted: hsl(0, 0%, 100%);

  /* Borders and Rings */
  --border-default: hsl(240, 6%, 90%);
  --border-strong: hsl(240, 8%, 80%);
  --ring-default: hsl(90, 50%, 50%);

  /* Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
}

.dark {
  /* Layout */
  --layout-bg: hsl(220, 4%, 14%);
  --layout-text: hsl(0, 0%, 90%);
  --layout-border: hsl(240, 8%, 15%);

  /* Menus/Sidebar */
  --menues-bg: hsl(220, 4%, 10%);
  --menues-fg: hsl(0, 0%, 70%);
  --menues-secondary-fg: hsl(0, 0%, 60%);
  --menues-border: hsl(240, 8%, 20%);
  --menues-hover-bg: hsl(0, 0%, 20%);
  --menues-hover-fg: hsl(0, 0%, 90%);
  --menues-active-bg: hsl(0, 0%, 25%);
  --menues-active-fg: hsl(0, 0%, 98%);

  /* Cards */
  --card-bg: hsl(220, 4%, 16%);
  --card-text: hsl(0, 0%, 90%);
  --card-border: hsl(240, 8%, 15%);
  --card-hover-bg: hsl(0, 0%, 16%);

  /* Buttons - Primary */
  --button-primary-bg: hsl(90, 50%, 50%);
  --button-primary-text: hsl(0, 0%, 100%);
  --button-primary-border: transparent;
  --button-primary-hover-bg: hsl(90, 50%, 50%);
  --button-primary-hover-text: hsl(0, 0%, 100%);
  --button-primary-hover-border: transparent;
  --button-primary-disabled-bg: hsl(240, 5%, 25%);
  --button-primary-disabled-text: hsl(240, 5%, 50%);

  /* Buttons - Secondary */
  --button-secondary-bg: hsl(240, 10%, 8%);
  --button-secondary-text: hsl(0, 0%, 98%);
  --button-secondary-border: hsl(240, 8%, 15%);
  --button-secondary-hover-bg: hsl(240, 8%, 12%);
  --button-secondary-hover-text: hsl(0, 0%, 98%);
  --button-secondary-hover-border: hsl(240, 8%, 25%);

  /* Buttons - Ghost */
  --button-ghost-bg: transparent;
  --button-ghost-text: hsl(0, 0%, 98%);
  --button-ghost-border: transparent;
  --button-ghost-hover-bg: hsl(240, 8%, 12%);
  --button-ghost-hover-text: hsl(0, 0%, 98%);
  --button-ghost-hover-border: transparent;

  /* Inputs */
  --input-bg: hsl(0, 0%, 12%);
  --input-text: hsl(0, 0%, 98%);
  --input-border: hsl(0, 0%, 25%);
  --input-placeholder: hsl(0, 0%, 50%);
  --input-focus-ring: hsl(90, 50 50%);
  --input-focus-border: hsl(90, 50%, 50%);

  /* Popover/Dropdown */
  --popover-bg: hsl(240, 10%, 8%);
  --popover-fg: hsl(0, 0%, 98%);
  --accent-bg: hsl(90, 30%, 20%);

  /* Accent / State */
  --accent: hsl(90, 50%, 50%);
  --accent-text: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 65%);
  --destructive-text: hsl(0, 0%, 100%);
  --success: hsl(120, 40%, 55%);
  --success-text: hsl(0, 0%, 100%);
  --warning: hsl(45, 90%, 55%);
  --warning-text: hsl(240, 10%, 4%);

  /* Charts */
  --chart-1: hsl(210, 40%, 55%);
  --chart-2: hsl(173, 58%, 45%);
  --chart-3: hsl(197, 37%, 35%);
  --chart-4: hsl(43, 74%, 70%);
  --chart-5: hsl(27, 87%, 70%);

  /* Text */
  --text-default: hsl(0, 0%, 98%);
  --text-muted: hsl(240, 5%, 65%);
  --text-subtle: hsl(240, 5%, 45%);
  --text-inverted: hsl(240, 10%, 4%);

  /* Borders and Rings */
  --border-default: hsl(240, 8%, 15%);
  --border-strong: hsl(240, 8%, 25%);
  --ring-default: hsl(0, 0%, 30%);

  /* Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
}

/* Avatar styles - aplicar borde de 3px a todos los avatares */
.avatar-border,
[data-avatar],
.avatar {
  border: 3px solid var(--card-border) !important;
}

@layer base {
  * {
    @apply border-[var(--border-default)];
  }

  body {
    @apply font-sans antialiased;
    background-color: var(--layout-bg);
    color: var(--layout-text);
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-rounded {
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }
}