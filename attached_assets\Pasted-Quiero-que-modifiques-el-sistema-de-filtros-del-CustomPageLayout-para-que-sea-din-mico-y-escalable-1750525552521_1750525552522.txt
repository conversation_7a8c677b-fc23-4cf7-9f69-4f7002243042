Quiero que modifiques el sistema de filtros del `CustomPageLayout` para que sea dinámico y escalable, manteniendo el botón de “Limpiar filtros” tal como está.

---

## ✅ OBJETIVO GENERAL:

1. El botón de “Filtros” debe abrir un `DropdownMenu` con contenido personalizado que define cada página.
2. El botón de “Limpiar filtros” debe mantenerse al lado, como ahora, y seguir funcionando.
3. Ese botón debe poder limpiar **todos los filtros** de una vez, aunque ahora haya más campos.

---

## 1. EN `CustomPageLayout.tsx`:

### Agregá una nueva prop:

```ts
customFilters?: React.ReactNode
Mantené las siguientes props como están:
ts
Copiar
Editar
onClearFilters?: () => void
Reemplazá el bloque de filtros actual por esto:
tsx
Copiar
Editar
{customFilters && (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline">
        <Filter className="mr-2 h-4 w-4" />
        Filtros
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent className="w-72 p-2 space-y-3">
      {customFilters}
    </DropdownMenuContent>
  </DropdownMenu>
)}

{onClearFilters && (
  <Button variant="ghost" onClick={onClearFilters}>
    Limpiar filtros
  </Button>
)}
Esto mantiene el mismo diseño de dos botones al lado del buscador: uno para abrir filtros, otro para limpiar.

2. EN CADA PÁGINA QUE USE CustomPageLayout:
Reemplazá el uso de filters (array) por customFilters (componente).

Mantené onClearFilters tal como está, pero ahora debe limpiar todos los estados de filtro que se definan en la página.

Ejemplo (SiteLogs.tsx):
tsx
Copiar
Editar
const [sortBy, setSortBy] = useState('date_desc')
const [onlyFavorites, setOnlyFavorites] = useState(false)

const handleClearFilters = () => {
  setSortBy('date_desc')
  setOnlyFavorites(false)
}

const filtersDropdown = (
  <div className="space-y-2">
    <div>
      <Label className="text-xs">Ordenar por</Label>
      <Select value={sortBy} onValueChange={setSortBy}>
        <SelectTrigger>
          <SelectValue placeholder="Seleccionar orden" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="date_desc">Fecha descendente</SelectItem>
          <SelectItem value="date_asc">Fecha ascendente</SelectItem>
          <SelectItem value="type">Tipo</SelectItem>
        </SelectContent>
      </Select>
    </div>
    <div className="flex items-center justify-between">
      <Label className="text-xs">Solo favoritas</Label>
      <Switch checked={onlyFavorites} onCheckedChange={setOnlyFavorites} />
    </div>
  </div>
)

<CustomPageLayout
  icon={FileText}
  title="Bitácora de Obra"
  actions={actions}
  searchValue={searchValue}
  onSearchChange={setSearchValue}
  customFilters={filtersDropdown}
  onClearFilters={handleClearFilters}
/>
✅ Resultado: el botón de “Limpiar filtros” sigue al lado del botón de filtros, pero ahora limpia múltiples campos a la vez.